package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.MiniUser;
import com.ruoyi.system.mapper.MiniUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.UserApplyMapper;
import com.ruoyi.system.domain.UserApply;
import com.ruoyi.system.service.IUserApplyService;

/**
 * 申请管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
@Service
public class UserApplyServiceImpl implements IUserApplyService {
    @Autowired
    private UserApplyMapper userApplyMapper;

    @Autowired
    private MiniUserMapper miniUserMapper;

    /**
     * 查询申请管理
     * 
     * @param userApplyId 申请管理主键
     * @return 申请管理
     */
    @Override
    public UserApply selectUserApplyByUserApplyId(Long userApplyId) {
        return userApplyMapper.selectUserApplyByUserApplyId(userApplyId);
    }

    /**
     * 查询申请管理列表
     * 
     * @param userApply 申请管理
     * @return 申请管理
     */
    @Override
    public List<UserApply> selectUserApplyList(UserApply userApply) {
        return userApplyMapper.selectUserApplyList(userApply);
    }

    /**
     * 新增申请管理
     * 
     * @param userApply 申请管理
     * @return 结果
     */
    @Override
    public int insertUserApply(UserApply userApply) {
        return userApplyMapper.insertUserApply(userApply);
    }

    /**
     * 修改申请管理
     * 
     * @param userApply 申请管理
     * @return 结果
     */
    @Override
    public int updateUserApply(UserApply userApply) {
        // 若同意申请，则更新用户信息
        if (StringUtils.isNotEmpty(userApply.getStatus())) {
            if ("已同意".equals(userApply.getStatus())) {
                MiniUser miniUser = new MiniUser();
                miniUser.setNickName(userApply.getNickName());
                miniUser.setAvatar(userApply.getAvatar());
                miniUser.setUserId(userApply.getUserId());
                if (userApply.getUserType() == 1) {
                    miniUser.setUserType(userApply.getUserType());
                    miniUser.setPhone(userApply.getPhone());
                    miniUser.setProvince(userApply.getCity());
                    miniUser.setSex(userApply.getSex());
                    miniUser.setAge(userApply.getAge());
                    miniUser.setVoice(userApply.getVoice());
                    miniUser.setVoiceTime(userApply.getVoiceTime());
                }
                miniUserMapper.updateMiniUser(miniUser);
            }
        }
        return userApplyMapper.updateUserApply(userApply);
    }

    /**
     * 批量删除申请管理
     * 
     * @param userApplyIds 需要删除的申请管理主键
     * @return 结果
     */
    @Override
    public int deleteUserApplyByUserApplyIds(Long[] userApplyIds) {
        return userApplyMapper.deleteUserApplyByUserApplyIds(userApplyIds);
    }

    /**
     * 删除申请管理信息
     *
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    @Override
    public int deleteUserApplyByUserApplyId(Long userApplyId) {
        return userApplyMapper.deleteUserApplyByUserApplyId(userApplyId);
    }

    /**
     * 同意申请
     *
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    @Override
    public int approveUserApply(Long userApplyId) {
        UserApply userApply = userApplyMapper.selectUserApplyByUserApplyId(userApplyId);
        if (userApply == null) {
            return 0;
        }

        // 更新申请状态为已同意
        userApply.setStatus("已同意");
        int result = userApplyMapper.updateUserApply(userApply);

        // 更新用户表中的用户类型为店员
        if (result > 0) {
            MiniUser miniUser = new MiniUser();
            miniUser.setUserId(userApply.getUserId());
            miniUser.setUserType(3); // 设置为店员
            miniUser.setNickName(userApply.getNickName());
            miniUser.setAvatar(userApply.getAvatar());
            miniUser.setPhone(userApply.getPhone());
            miniUser.setProvince(userApply.getCity());
            miniUser.setSex(userApply.getSex());
            miniUser.setAge(userApply.getAge());
            miniUser.setVoice(userApply.getVoice());
            miniUser.setVoiceTime(userApply.getVoiceTime());
            miniUser.setWechat(userApply.getWechat());
            miniUser.setTags(userApply.getTags());
            miniUser.setSignature(userApply.getSignature());
            miniUser.setLevel(userApply.getLevel());
            miniUserMapper.updateMiniUser(miniUser);
        }

        return result;
    }

    /**
     * 拒绝申请
     *
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    @Override
    public int rejectUserApply(Long userApplyId) {
        UserApply userApply = userApplyMapper.selectUserApplyByUserApplyId(userApplyId);
        if (userApply == null) {
            return 0;
        }

        // 更新申请状态为已拒绝
        userApply.setStatus("已拒绝");
        return userApplyMapper.updateUserApply(userApply);
    }
}
