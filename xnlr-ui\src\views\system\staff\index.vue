<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#409EFF"><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalStaff }}</div>
              <div class="stats-label">总店员数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#67C23A"><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ activeStaff }}</div>
              <div class="stats-label">在线店员</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#E6A23C"><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ topStaff }}</div>
              <div class="stats-label">优秀店员</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#F56C6C"><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ totalEarnings }}</div>
              <div class="stats-label">总收益</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="search-card mb-4">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        label-width="80px"
      >
        <el-form-item label="店员昵称" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入店员昵称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select
            v-model="queryParams.sex"
            placeholder="请选择性别"
            clearable
          >
            <el-option label="男" value="0" />
            <el-option label="女" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="在线状态" prop="isOnline">
          <el-select
            v-model="queryParams.isOnline"
            placeholder="请选择在线状态"
            clearable
          >
            <el-option label="在线" :value="1" />
            <el-option label="离线" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="mb-4">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:staff:export']"
          >
            导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
    </el-card>

    <!-- 店员列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">店员列表</span>
          <span class="card-subtitle">共 {{ total }} 名店员</span>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="staffList"
        @selection-change="handleSelectionChange"
        class="staff-table"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="店员信息" width="200" align="center">
          <template #default="scope">
            <div class="staff-info">
              <el-avatar
                :size="50"
                :src="scope.row.avatar"
                class="staff-avatar"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="staff-details">
                <div class="staff-name">{{ scope.row.nickName }}</div>
                <div class="staff-id">ID: {{ scope.row.userId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center">
          <template #default="scope">
            <div class="contact-info">
              <div v-if="scope.row.phone">
                <el-icon><Phone /></el-icon>
                {{ scope.row.phone }}
              </div>
              <div v-if="scope.row.wechat">
                <el-icon><ChatDotRound /></el-icon>
                {{ scope.row.wechat }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="基本信息" align="center">
          <template #default="scope">
            <div class="basic-info">
              <el-tag
                :type="scope.row.sex === '0' ? 'primary' : 'danger'"
                size="small"
              >
                {{ scope.row.sex === "0" ? "男" : "女" }}
              </el-tag>
              <span class="ml-2">{{ scope.row.age }}岁</span>
              <div class="mt-1">{{ scope.row.province }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务信息" align="center">
          <template #default="scope">
            <div class="service-info">
              <div class="level">等级: {{ scope.row.level || "普通" }}</div>
              <div class="price">起步价: ¥{{ scope.row.basePrice || 0 }}</div>
              <div class="earnings">
                总收益: ¥{{ scope.row.totalAmount || 0 }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.isOnline ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.isOnline ? "在线" : "离线" }}
            </el-tag>
            <div class="mt-1">
              <el-tag
                :type="scope.row.enabled ? 'success' : 'danger'"
                size="small"
              >
                {{ scope.row.enabled ? "启用" : "禁用" }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="200"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleView(scope.row)"
              v-hasPermi="['system:staff:query']"
            >
              查看
            </el-button>
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:staff:edit']"
            >
              编辑
            </el-button>
            <el-button
              link
              type="primary"
              icon="Switch"
              @click="handleStatusChange(scope.row)"
              v-hasPermi="['system:staff:edit']"
            >
              {{ scope.row.enabled ? "禁用" : "启用" }}
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:staff:remove']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="Staff">
import {
  listStaff,
  getStaff,
  updateStaff,
  delStaff,
  changeStaffStatus,
} from "@/api/system/staff";
import {
  User,
  CircleCheck,
  Star,
  Money,
  Phone,
  ChatDotRound,
} from "@element-plus/icons-vue";

const { proxy } = getCurrentInstance();

const staffList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 统计数据
const totalStaff = ref(0);
const activeStaff = ref(0);
const topStaff = ref(0);
const totalEarnings = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nickName: null,
    phone: null,
    sex: null,
    isOnline: null,
    userType: 1, // 店员类型
  },
});

const { queryParams } = toRefs(data);

/** 查询店员列表 */
function getList() {
  loading.value = true;
  listStaff(queryParams.value).then((response) => {
    staffList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 计算统计数据
    calculateStats();
  });
}

/** 计算统计数据 */
function calculateStats() {
  totalStaff.value = staffList.value.length;
  activeStaff.value = staffList.value.filter((staff) => staff.isOnline).length;
  topStaff.value = staffList.value.filter(
    (staff) => staff.level && staff.level !== "普通"
  ).length;
  totalEarnings.value = staffList.value.reduce(
    (sum, staff) => sum + (staff.totalAmount || 0),
    0
  );
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  // 实现查看详情逻辑
  proxy.$modal.msgInfo("查看店员详情功能待实现");
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 实现编辑逻辑
  proxy.$modal.msgInfo("编辑店员功能待实现");
}

/** 状态切换 */
function handleStatusChange(row) {
  const text = row.enabled ? "禁用" : "启用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.nickName + '"店员吗？')
    .then(function () {
      const updateData = {
        userId: row.userId,
        enabled: row.enabled ? 0 : 1,
      };
      return changeStaffStatus(row.userId, row.enabled ? 0 : 1);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.userId || ids.value;
  proxy.$modal
    .confirm('是否确认删除店员编号为"' + userIds + '"的数据项？')
    .then(function () {
      return delStaff(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/staff/export",
    {
      ...queryParams.value,
    },
    `staff_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>

<style scoped>
.stats-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stats-icon {
  margin-right: 16px;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.card-subtitle {
  font-size: 14px;
  color: #909399;
}

.staff-table {
  border-radius: 8px;
}

.staff-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.staff-avatar {
  border: 2px solid #f0f0f0;
}

.staff-details {
  text-align: left;
}

.staff-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.staff-id {
  font-size: 12px;
  color: #909399;
}

.contact-info div {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 14px;
}

.basic-info {
  text-align: center;
}

.service-info div {
  margin-bottom: 4px;
  font-size: 14px;
}

.level {
  color: #409eff;
  font-weight: bold;
}

.price {
  color: #e6a23c;
}

.earnings {
  color: #67c23a;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-1 {
  margin-top: 4px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
