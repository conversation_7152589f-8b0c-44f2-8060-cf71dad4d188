package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MiniUserMapper;
import com.ruoyi.system.domain.MiniUser;
import com.ruoyi.system.service.IMiniUserService;

/**
 * 用户管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
@Service
public class MiniUserServiceImpl implements IMiniUserService 
{
    @Autowired
    private MiniUserMapper miniUserMapper;

    /**
     * 查询用户管理
     * 
     * @param userId 用户管理主键
     * @return 用户管理
     */
    @Override
    public MiniUser selectMiniUserByUserId(String userId)
    {
        return miniUserMapper.selectMiniUserByUserId(userId);
    }

    /**
     * 查询用户管理列表
     * 
     * @param miniUser 用户管理
     * @return 用户管理
     */
    @Override
    public List<MiniUser> selectMiniUserList(MiniUser miniUser)
    {
        return miniUserMapper.selectMiniUserList(miniUser);
    }

    /**
     * 新增用户管理
     * 
     * @param miniUser 用户管理
     * @return 结果
     */
    @Override
    public int insertMiniUser(MiniUser miniUser)
    {
        return miniUserMapper.insertMiniUser(miniUser);
    }

    /**
     * 修改用户管理
     * 
     * @param miniUser 用户管理
     * @return 结果
     */
    @Override
    public int updateMiniUser(MiniUser miniUser)
    {
        return miniUserMapper.updateMiniUser(miniUser);
    }

    /**
     * 批量删除用户管理
     * 
     * @param userIds 需要删除的用户管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniUserByUserIds(String[] userIds)
    {
        return miniUserMapper.deleteMiniUserByUserIds(userIds);
    }

    /**
     * 删除用户管理信息
     * 
     * @param userId 用户管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniUserByUserId(String userId)
    {
        return miniUserMapper.deleteMiniUserByUserId(userId);
    }
}
