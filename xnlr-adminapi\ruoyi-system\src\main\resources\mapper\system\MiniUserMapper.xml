<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MiniUserMapper">
    
    <resultMap type="MiniUser" id="MiniUserResult">
        <result property="userId"    column="user_id"    />
        <result property="openid"    column="openid"    />
        <result property="nickName"    column="nick_name"    />
        <result property="avatar"    column="avatar"    />
        <result property="wechat"    column="wechat"    />
        <result property="phone"    column="phone"    />
        <result property="balance"    column="balance"    />
        <result property="userType"    column="user_type"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="province"    column="province"    />
        <result property="tags"    column="tags"    />
        <result property="isOnline"    column="is_online"    />
        <result property="basePrice"    column="base_price"    />
        <result property="signature"    column="signature"    />
        <result property="level"    column="level"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="voice"    column="voice"    />
        <result property="voiceTime"    column="voice_time"    />
        <result property="enabled"    column="enabled"    />
    </resultMap>

    <sql id="selectMiniUserVo">
        select user_id, openid, nick_name, avatar, wechat, phone, balance, user_type, created_at, updated_at, sex, age, province, tags, is_online, base_price, signature, level, total_amount, voice, voice_time, enabled from user
    </sql>

    <select id="selectMiniUserList" parameterType="MiniUser" resultMap="MiniUserResult">
        <include refid="selectMiniUserVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="wechat != null  and wechat != ''"> and wechat = #{wechat}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="tags != null  and tags != ''"> and tags = #{tags}</if>
            <if test="isOnline != null "> and is_online = #{isOnline}</if>
            <if test="basePrice != null "> and base_price = #{basePrice}</if>
            <if test="signature != null  and signature != ''"> and signature = #{signature}</if>
            <if test="level != null  and level != ''"> and level = #{level}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="voice != null  and voice != ''"> and voice = #{voice}</if>
            <if test="voiceTime != null "> and voice_time = #{voiceTime}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
        </where>
    </select>
    
    <select id="selectMiniUserByUserId" parameterType="String" resultMap="MiniUserResult">
        <include refid="selectMiniUserVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertMiniUser" parameterType="MiniUser" useGeneratedKeys="true" keyProperty="userId">
        insert into user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="wechat != null">wechat,</if>
            <if test="phone != null">phone,</if>
            <if test="balance != null">balance,</if>
            <if test="userType != null">user_type,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="province != null">province,</if>
            <if test="tags != null">tags,</if>
            <if test="isOnline != null">is_online,</if>
            <if test="basePrice != null">base_price,</if>
            <if test="signature != null">signature,</if>
            <if test="level != null">level,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="voice != null">voice,</if>
            <if test="voiceTime != null">voice_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="wechat != null">#{wechat},</if>
            <if test="phone != null">#{phone},</if>
            <if test="balance != null">#{balance},</if>
            <if test="userType != null">#{userType},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="province != null">#{province},</if>
            <if test="tags != null">#{tags},</if>
            <if test="isOnline != null">#{isOnline},</if>
            <if test="basePrice != null">#{basePrice},</if>
            <if test="signature != null">#{signature},</if>
            <if test="level != null">#{level},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="voice != null">#{voice},</if>
            <if test="voiceTime != null">#{voiceTime},</if>
         </trim>
    </insert>

    <update id="updateMiniUser" parameterType="MiniUser">
        update user
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="wechat != null">wechat = #{wechat},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="province != null">province = #{province},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="isOnline != null">is_online = #{isOnline},</if>
            <if test="basePrice != null">base_price = #{basePrice},</if>
            <if test="signature != null">signature = #{signature},</if>
            <if test="level != null">level = #{level},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="voice != null">voice = #{voice},</if>
            <if test="voiceTime != null">voice_time = #{voiceTime},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteMiniUserByUserId" parameterType="String">
        delete from user where user_id = #{userId}
    </delete>

    <delete id="deleteMiniUserByUserIds" parameterType="String">
        delete from user where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>