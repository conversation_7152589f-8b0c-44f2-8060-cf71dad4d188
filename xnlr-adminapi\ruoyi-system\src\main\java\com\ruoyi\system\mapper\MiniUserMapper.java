package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MiniUser;

/**
 * 用户管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public interface MiniUserMapper 
{
    /**
     * 查询用户管理
     * 
     * @param userId 用户管理主键
     * @return 用户管理
     */
    public MiniUser selectMiniUserByUserId(String userId);

    /**
     * 查询用户管理列表
     * 
     * @param miniUser 用户管理
     * @return 用户管理集合
     */
    public List<MiniUser> selectMiniUserList(MiniUser miniUser);

    /**
     * 新增用户管理
     * 
     * @param miniUser 用户管理
     * @return 结果
     */
    public int insertMiniUser(MiniUser miniUser);

    /**
     * 修改用户管理
     * 
     * @param miniUser 用户管理
     * @return 结果
     */
    public int updateMiniUser(MiniUser miniUser);

    /**
     * 删除用户管理
     * 
     * @param userId 用户管理主键
     * @return 结果
     */
    public int deleteMiniUserByUserId(String userId);

    /**
     * 批量删除用户管理
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniUserByUserIds(String[] userIds);
}
